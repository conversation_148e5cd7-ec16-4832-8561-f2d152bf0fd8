import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function MortgagePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">房貸計算</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 表單區域 */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">房貸資訊</h2>
              {/* 這裡將放置 MortgageForm 組件 */}
              <div className="text-center text-gray-500 py-8">
                房貸表單組件開發中...
              </div>
            </div>

            {/* 結果區域 */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-6">計算結果</h2>
              {/* 這裡將放置計算結果和圖表 */}
              <div className="text-center text-gray-500 py-8">
                計算結果和圖表將顯示在這裡
              </div>
            </div>
          </div>

          {/* 還款計劃表 */}
          <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6">還款計劃表</h2>
            {/* 這裡將放置 MortgageTable 組件 */}
            <div className="text-center text-gray-500 py-8">
              還款計劃表組件開發中...
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}