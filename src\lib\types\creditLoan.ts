export interface CreditLoanInput {
  amount: number;
  months: number;
  rateType: 'single' | 'two-stage' | 'three-stage';
  rates: {
    rate1: number;
    months1?: number;
    rate2?: number;
    months2?: number;
    rate3?: number;
  };
  fees: {
    setupFee: number;
    managementFee: number;
    otherFees: number;
  };
}

export interface PaymentScheduleItem {
  period: number;
  interest: number;
  principal: number;
  payment: number;
  remainingBalance: number;
}

export interface CreditLoanResult {
  monthlyPayment: number;
  totalInterest: number;
  totalPayment: number;
  totalFees: number;
  effectiveApr: number;
  schedule: PaymentScheduleItem[];
}