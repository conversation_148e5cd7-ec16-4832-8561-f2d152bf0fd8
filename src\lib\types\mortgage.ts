export interface MortgageInput {
  housePrice: number;
  downPayment: number;
  loanAmount: number;
  loanYears: number;
  interestRate: number;
  graceYears: number;
  fees: {
    notaryFee: number;
    registrationFee: number;
    fireInsurance: number;
    earthquakeInsurance: number;
    appraisalFee: number;
  };
}

export interface MortgageResult {
  monthlyPayment: number;
  gracePayment: number;
  totalInterest: number;
  totalPayment: number;
  totalFees: number;
  schedule: PaymentScheduleItem[];
}